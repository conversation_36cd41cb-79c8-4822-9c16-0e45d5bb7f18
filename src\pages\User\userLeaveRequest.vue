<template>
  <UserNavigation />
  <div class="q-pa-md">
    <div class="container q-pa-md">
      <div class="row q-col-gutter-md justify-between">
        <!-- Left Column - Calendar -->
        <div
          class="col-12 col-md-7 bg-dg q-pa-md q-mt-md"
          style="border-radius: 10px; width: 100%; max-width: 800px; min-width: 350px"
        >
          <q-card flat class="calendar-container">
            <q-card-section>
              <!-- Month Navigation -->
              <div class="month-navigation">
                <q-btn flat round icon="chevron_left" @click="previousMonth" class="nav-btn" />
                <div class="month-title">{{ currentMonthDisplay }}</div>
                <q-btn flat round icon="chevron_right" @click="nextMonth" class="nav-btn" />
              </div>

              <!-- Calendar Grid -->
              <div class="calendar-grid">
                <!-- Day Headers -->
                <div class="day-headers">
                  <div v-for="day in dayHeaders" :key="day" class="day-header">
                    {{ day }}
                  </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="calendar-loading">
                  <q-spinner color="primary" size="2em" />
                  <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
                </div>

                <!-- Calendar Days -->
                <div v-else class="calendar-days">
                  <div
                    v-for="day in calendarDays"
                    :key="`${day.date}-${day.month}`"
                    :class="[
                      'calendar-day',
                      {
                        today: day.isToday,
                        selected:
                          selectedDate &&
                          day.date === selectedDate.date &&
                          day.month === selectedDate.month &&
                          day.year === selectedDate.year,
                        'other-month': day.isOtherMonth,
                      },
                    ]"
                    @click="selectDate(day)"
                  >
                    <div
                      v-if="day.status"
                      :class="['status-indicator', `status-${day.status}`]"
                    ></div>
                    <span class="day-number">{{ day.date }}</span>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Right Column - Header and Actions -->
        <div class="col-12 col-md-5" style="width: 100%; max-width: 700px; min-width: 350px">
          <!-- Date Header -->
          <q-card flat class="date-header-card bg-main">
            <q-card-section class="row items-center justify-between">
              <div class="row items-center">
                <q-icon name="edit" size="sm" class="q-mr-sm" color="white" />
                <span class="date-header-text text-white">{{ selectedDateDisplay }}</span>
              </div>
              <div class="row items-center">
                <q-btn flat round icon="close" color="white" @click="clearSelection" />
              </div>
            </q-card-section>
          </q-card>

          <div
            class="bg-dg q-pa-md"
            style="border-bottom-right-radius: 10px; border-bottom-left-radius: 10px"
          >
            <q-card flat class="stats-card">
              <q-card-section>
                <div class="stats-title">จำนวนวันลาที่เหลือ</div>
                <div class="stats-row">
                  <div class="stat-item">
                    <div class="stat-label text-negative">ลากิจ</div>
                    <div class="stat-value">{{ leaveStats.personal }}</div>
                    <div class="stat-unit">วัน</div>
                  </div>
                  <div class="stat-divider"></div>
                  <div class="stat-item">
                    <div class="stat-label text-blue">ลาป่วย</div>
                    <div class="stat-value">{{ leaveStats.sick }}</div>
                    <div class="stat-unit">วัน</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>

            <!-- Action Buttons -->
            <div class="action-buttons q-mt-md">
              <q-btn
                class="action-btn schedule-btn bg-main justify-center"
                @click="openScheduleDialog"
                no-caps
                flat
              >
                <div class="row items-center full-width justify-start" style="position: relative">
                  <q-icon
                    name="event_note"
                    class="q-ml-sm"
                    size="xl"
                    style="position: absolute; left: 0"
                  />
                  <div class="text-center full-width">ลงตารางงาน</div>
                </div>
              </q-btn>

              <q-btn
                class="action-btn leave-btn bg-blue q-mt-sm justify-center"
                @click="openLeaveDialog"
                no-caps
                flat
              >
                <div class="row items-center full-width justify-start" style="position: relative">
                  <q-icon
                    name="event_busy"
                    class="q-ml-sm"
                    size="xl"
                    style="position: absolute; left: 0"
                  />
                  <div class="text-center full-width">ลงวันลา</div>
                </div>
              </q-btn>

              <q-btn
                class="action-btn holiday-btn bg-negative q-mt-sm justify-center"
                @click="openHolidayDialog"
                no-caps
                flat
              >
                <div class="row items-center full-width justify-start" style="position: relative">
                  <q-icon
                    name="warning"
                    class="q-ml-sm"
                    size="xl"
                    style="position: absolute; left: 0"
                  />
                  <div class="text-center full-width">ลงวันหยุด</div>
                </div>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Leave Request Dialog -->
  <UserLeaveDialog
    v-model="showLeaveDialog"
    :user-id="userId"
    :selected-date="selectedDate"
    @success="handleLeaveSuccess"
  />

  <!-- Schedule Dialog -->
  <UserScheduleDialog
    v-model="showScheduleDialog"
    :user-id="userId"
    @success="handleScheduleSuccess"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { date } from 'quasar'
import { useRoute } from 'vue-router'
import UserNavigation from 'src/components/userNavigation.vue'
import UserLeaveDialog from './dialog/userLeaveDialog.vue'
import UserScheduleDialog from './dialog/userScheduleDialog.vue'
import { useUserStore } from 'src/stores/userStore'
import { useAttendanceStore } from 'src/stores/attendance'
import { LeaveRequestService } from 'src/services/leaveRequestService'
import { UserService } from 'src/services/userService'
import type { AttendanceRecord } from 'src/types/attendance'

// Types
interface CalendarDay {
  date: number
  month: number
  year: number
  isOtherMonth: boolean
  isToday: boolean
  status: string | null
}

interface LeaveRequest {
  id: number
  leave_date: string
  leave_type: string
  reason?: string
  created_at: string
}

interface ExtendedUser {
  id: number
  name: string
  password: string
  tel: string
  role: string
  hour_work: number
  sick_level?: number
  personal_leave: number
  sick_leave_remaining?: number
  personal_leave_remaining?: number
  image: string
  day_off?: string
  email?: string
  branch: {
    id: number
    name: string
    address?: string
  }
}

// Router and Store
const route = useRoute()
const userStore = useUserStore()
const attendanceStore = useAttendanceStore()

// Dialog state
const showLeaveDialog = ref(false)
const showScheduleDialog = ref(false)

// Reactive data
const currentDate = ref(new Date())
// Initialize with current date as default selection
const selectedDate = ref<{ date: number; month: number; year: number } | null>({
  date: new Date().getDate(),
  month: new Date().getMonth(),
  year: new Date().getFullYear(),
})
const leaveRequests = ref<LeaveRequest[]>([])
const attendanceRecords = ref<AttendanceRecord[]>([])
const targetUser = ref<ExtendedUser | null>(null) // User to display data for
const loading = ref(false)

// Get user ID from route parameter
const userId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : null
})

// Day headers for calendar
const dayHeaders = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su']

// Computed leave statistics from target user data
const leaveStats = computed(() => {
  if (!targetUser.value) {
    return { personal: 0, sick: 0 }
  }

  return {
    personal: targetUser.value.personal_leave_remaining || 0,
    sick: targetUser.value.sick_leave_remaining || 0,
  }
})

// Computed properties
const currentMonthDisplay = computed(() => {
  const monthNames = [
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม',
  ]
  const year = currentDate.value.getFullYear() + 543 // Thai Buddhist year
  const month = monthNames[currentDate.value.getMonth()]
  return `${month} ${year}`
})

const selectedDateDisplay = computed(() => {
  const monthNames = [
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม',
  ]

  if (!selectedDate.value) {
    // Show current date when no date is selected
    const today = new Date()
    const year = today.getFullYear() + 543
    const month = monthNames[today.getMonth()]
    return `วันที่ ${today.getDate()} ${month} ${year}`
  }

  // Show selected date
  const year = selectedDate.value.year + 543
  const month = monthNames[selectedDate.value.month]
  const displayText = `วันที่ ${selectedDate.value.date} ${month} ${year}`

  console.log('Selected date display:', displayText, selectedDate.value) // Debug log

  return displayText
})

const calendarDays = computed((): CalendarDay[] => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // Get first day of month and calculate starting point
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)

  // Adjust to start from Monday (1 = Monday, 0 = Sunday)
  const dayOfWeek = (firstDay.getDay() + 6) % 7
  startDate.setDate(startDate.getDate() - dayOfWeek)

  const days: CalendarDay[] = []
  const today = new Date()

  // Generate 42 days (6 weeks)
  for (let i = 0; i < 42; i++) {
    const currentDay = new Date(startDate)
    currentDay.setDate(startDate.getDate() + i)

    const isCurrentMonth = currentDay.getMonth() === month
    const isToday = date.isSameDate(currentDay, today, 'day')

    days.push({
      date: currentDay.getDate(),
      month: currentDay.getMonth(),
      year: currentDay.getFullYear(),
      isOtherMonth: !isCurrentMonth,
      isToday,
      status: getDateStatus(currentDay),
    })
  }

  return days
})

// Helper function to get day of week name
const getDayOfWeekName = (date: Date): string => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  return days[date.getDay()] || 'Sunday'
}

// Helper function for timezone-safe date formatting to YYYY-MM-DD
const formatDateToString = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// Methods
const getDateStatus = (date: Date): string | null => {
  // Use timezone-safe date formatting to avoid UTC conversion issues
  const dateString = formatDateToString(date)

  // Use targetUser instead of currentUser to get the correct user's day_off
  const userToCheck = targetUser.value

  if (!userToCheck) return null

  // Check if the date is in the current month being displayed
  const currentMonth = currentDate.value.getMonth()
  const currentYear = currentDate.value.getFullYear()
  const dateMonth = date.getMonth()
  const dateYear = date.getFullYear()

  // Don't show indicators for dates from other months
  if (dateMonth !== currentMonth || dateYear !== currentYear) {
    return null
  }

  // Check if it's a leave day (from leave requests)
  const isLeaveDay = leaveRequests.value.some((request) => request.leave_date === dateString)

  // Debug logging for date comparison
  if (leaveRequests.value.length > 0) {
    console.log(
      `Checking date ${dateString} against leave requests:`,
      leaveRequests.value.map((r) => r.leave_date),
    )
  }

  if (isLeaveDay) {
    console.log(`✅ Date ${dateString} is a leave day (matched with database)`) // Debug log
    return 'leave'
  }

  // Check if it's user's day off (holiday)
  const dayOfWeek = getDayOfWeekName(date)
  if (userToCheck.day_off === dayOfWeek) {
    return 'holiday'
  }

  // If it's not a leave day and not a holiday, it's a working day
  // Show green dot for all working days (past, present, and future)
  return 'work'
}

const selectDate = (day: CalendarDay) => {
  if (day.isOtherMonth) return

  console.log('Selecting date:', day) // Debug log

  selectedDate.value = {
    date: day.date,
    month: day.month,
    year: day.year,
  }

  console.log('Selected date updated to:', selectedDate.value) // Debug log
}

const clearSelection = () => {
  // Reset to current date as default
  const today = new Date()
  selectedDate.value = {
    date: today.getDate(),
    month: today.getMonth(),
    year: today.getFullYear(),
  }
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

const openScheduleDialog = () => {
  showScheduleDialog.value = true
}

const openLeaveDialog = () => {
  showLeaveDialog.value = true
}

const openHolidayDialog = () => {
  console.log('Open holiday dialog')
}

// Handle leave request success
const handleLeaveSuccess = async () => {
  if (!userId.value) return

  console.log('Leave request successful, refreshing data...') // Debug log

  try {
    // Refresh leave requests to update the calendar dots
    await fetchLeaveRequests(userId.value)

    // Refresh user info to update leave balance
    await fetchUserInfo(userId.value)

    console.log('Data refreshed successfully') // Debug log
    console.log('Updated leave requests:', leaveRequests.value) // Debug log
  } catch (error) {
    console.error('Error refreshing data after leave request:', error)
  }
}

// Handle schedule success
const handleScheduleSuccess = async () => {
  if (!userId.value) return

  console.log('Schedule updated successfully') // Debug log

  try {
    // Refresh user info to get updated schedule data
    await fetchUserInfo(userId.value)

    console.log('User data refreshed after schedule update') // Debug log
  } catch (error) {
    console.error('Error refreshing data after schedule update:', error)
  }
}

const fetchUserInfo = async (targetUserId: number) => {
  loading.value = true
  try {
    // Use UserService.getUserInfo directly without affecting the logged-in user
    const userData = await UserService.getUserInfo(targetUserId)
    targetUser.value = userData as ExtendedUser
  } catch (error) {
    console.error('Error fetching user info:', error)
    // Fallback: try to find user in the existing users list
    try {
      await userStore.fetchUsers()
      const foundUser = userStore.users.find((user) => user.id === targetUserId)
      if (foundUser) {
        targetUser.value = foundUser as ExtendedUser
      } else {
        targetUser.value = null
      }
    } catch (fallbackError) {
      console.error('Fallback user fetch failed:', fallbackError)
      targetUser.value = null
    }
  } finally {
    loading.value = false
  }
}

const fetchLeaveRequests = async (targetUserId: number) => {
  loading.value = true
  try {
    const requests = await LeaveRequestService.getUserLeaveRequests(targetUserId)
    leaveRequests.value = requests
  } catch (error) {
    console.error('Error fetching leave requests:', error)
    leaveRequests.value = []
  } finally {
    loading.value = false
  }
}

const fetchAttendanceData = async (targetUserId: number) => {
  loading.value = true
  try {
    // Use the attendance store's fetchUserAttendance method
    await attendanceStore.fetchUserAttendance(targetUserId)
    // Get the attendance records from the store
    attendanceRecords.value = attendanceStore.attendanceRecords
  } catch (error) {
    console.error('Error fetching attendance data:', error)
    attendanceRecords.value = []
  } finally {
    loading.value = false
  }
}

const initializeData = async (targetUserId: number) => {
  if (!targetUserId) {
    console.warn('No user ID provided')
    return
  }

  loading.value = true
  try {
    // Fetch user info, leave requests and attendance data
    await Promise.all([
      fetchUserInfo(targetUserId),
      fetchLeaveRequests(targetUserId),
      fetchAttendanceData(targetUserId),
    ])
  } catch (error) {
    console.error('Error initializing calendar data:', error)
  } finally {
    loading.value = false
  }
}

// Watch for route parameter changes
watch(
  userId,
  async (newUserId) => {
    if (newUserId) {
      await initializeData(newUserId)
    }
  },
  { immediate: true },
)

// Lifecycle
onMounted(async () => {
  // Data will be loaded via the userId watcher
  if (userId.value) {
    await initializeData(userId.value)
  }
})
</script>

<style scoped>
/* User Info Card */
.user-info-card {
  background-color: white;
  border-radius: 10px;
  border-left: 4px solid #294888;
}

/* Calendar Container */
.calendar-container {
  background-color: white;
  border-radius: 10px;
  width: 100%;
  max-width: 800px;
  min-width: 350px;
  min-height: 580px;
  height: 100%;
}

/* Month Navigation */
.month-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px 0;
}

.month-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #294888;
}

.nav-btn {
  color: #294888;
}

.nav-btn:hover {
  background-color: rgba(41, 72, 136, 0.1);
}

/* Calendar Grid */
.calendar-grid {
  width: 100%;
}

.day-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 10px;
}

.day-header {
  text-align: center;
  font-weight: bold;
  color: #666;
  padding: 8px;
  font-size: 0.9rem;
}

.calendar-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  padding: 10px;
  background-color: white;
}

.calendar-day {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 50px;
  padding: 8px 4px;
}

.calendar-day:hover:not(.other-month) {
  background-color: rgba(145, 210, 193, 0.2);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-day.today {
  background-color: #91d2c1;
  color: white;
  font-weight: bold;
  border-radius: 8px;
}

.calendar-day.selected {
  background-color: #294888 !important;
  color: white !important;
  font-weight: bold;
  border-radius: 8px;
}

.calendar-day.other-month {
  opacity: 0.3;
  pointer-events: none;
}

.calendar-day.other-month .day-number {
  color: #999;
}

.calendar-day.other-month {
  opacity: 0.5;
}

.day-number {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  margin-top: 4px;
}

.calendar-day.today .day-number,
.calendar-day.selected .day-number {
  color: white;
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-bottom: 2px;
  flex-shrink: 0;
}

.status-work {
  background-color: #4caf50; /* Green for work/present days */
}

.status-leave {
  background-color: #d32f2f; /* Red for leave days */
}

.status-holiday {
  background-color: #2196f3; /* Blue for holiday days */
}

.status-present {
  background-color: #4caf50; /* Green for present days */
}

/* Date Header Card */
.date-header-card {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  color: white;
}

.date-header-text {
  font-size: 1.1rem;
  font-weight: bold;
}

/* Statistics Card */
.stats-card {
  background-color: white;
  border-radius: 10px;
}

.stats-title {
  text-align: center;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 0.9rem;
  margin-bottom: 5px;
  font-weight: 500;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 2px;
}

.stat-unit {
  font-size: 0.8rem;
  color: #666;
}

.stat-divider {
  width: 1px;
  height: 60px;
  background-color: #e0e0e0;
  margin: 0 15px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 100%;
  height: 105px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: bold;
  color: white;
  text-transform: none;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.schedule-btn {
  background-color: #91d2c1;
}

.leave-btn {
  background-color: #83a7d8;
}

.holiday-btn {
  background-color: #b53638;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calendar-days {
    gap: 6px;
    padding: 8px;
  }

  .calendar-day {
    min-height: 45px;
    padding: 6px 3px;
  }

  .day-number {
    font-size: 1rem;
  }

  .status-indicator {
    width: 10px;
    height: 10px;
  }

  .stat-value {
    font-size: 2rem;
  }

  .action-btn {
    height: 50px;
    font-size: 0.9rem;
  }

  .month-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .calendar-days {
    gap: 4px;
    padding: 6px;
  }

  .calendar-day {
    min-height: 40px;
    padding: 4px 2px;
  }

  .day-number {
    font-size: 0.9rem;
  }

  .status-indicator {
    width: 8px;
    height: 8px;
  }

  .stat-value {
    font-size: 1.8rem;
  }

  .action-btn {
    height: 45px;
    font-size: 0.85rem;
  }
}

@media (max-width: 1440px) {
  .container {
    padding-left: 36px !important;
    padding-right: 36px !important;
  }
  .row.justify-between {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    align-items: stretch !important;
    gap: 16px !important;
  }
  .col-12.col-md-7,
  .col-12.col-md-5 {
    max-width: 50% !important;
    min-width: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 50% !important;
    box-sizing: border-box;
  }
  .calendar-container,
  .date-header-card,
  .bg-dg.q-pa-md {
    max-width: 100% !important;
    min-width: 0 !important;
  }
}
</style>
