import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as d3 from 'd3'

export interface ChartDimensions {
  width: number
  height: number
  margin: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

export interface DonutChartData {
  label: string
  value: number
  color: string
}

export interface BarChartData {
  label: string
  value: number
  id?: string | number
}

// Default chart dimensions
export const defaultDimensions: ChartDimensions = {
  width: 400,
  height: 300,
  margin: { top: 20, right: 20, bottom: 40, left: 40 },
}

// Animation duration constants
export const ANIMATION_DURATION = 750
export const HOVER_DURATION = 200

// Color schemes
export const colorSchemes = {
  primary: ['#26A69A', '#AB47BC', '#42A5F5', '#66BB6A', '#FFA726'],
  performance: ['#439E62', '#83A7D8', '#ED9B53', '#E57373'],
  gradient: ['#1E8E7E', '#26A69A', '#4DB6AC', '#80CBC4'],
}

/**
 * Composable for D3.js chart utilities
 */
export function useD3Charts() {
  const resizeObserver = ref<ResizeObserver | null>(null)

  /**
   * Create responsive SVG container
   */
  const createSvg = (
    container: HTMLElement,
    dimensions: ChartDimensions,
  ): d3.Selection<SVGSVGElement, unknown, null, undefined> => {
    // Remove existing SVG
    d3.select(container).select('svg').remove()

    const svg = d3
      .select(container)
      .append('svg')
      .attr('width', dimensions.width)
      .attr('height', dimensions.height)
      .style('background', 'transparent')

    return svg
  }

  /**
   * Create animated tooltip
   */
  const createTooltip = () => {
    // Remove existing tooltip
    d3.select('body').select('.d3-tooltip').remove()

    return d3
      .select('body')
      .append('div')
      .attr('class', 'd3-tooltip')
      .style('position', 'absolute')
      .style('padding', '10px')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('border-radius', '5px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('opacity', 0)
      .style('z-index', '1000')
      .style('transition', 'opacity 0.2s ease')
  }

  /**
   * Show tooltip with animation
   */
  const showTooltip = (
    tooltip: d3.Selection<HTMLDivElement, unknown, HTMLElement, any>,
    content: string,
    event: MouseEvent,
  ) => {
    tooltip
      .style('left', `${event.pageX + 10}px`)
      .style('top', `${event.pageY - 10}px`)
      .html(content)
      .transition()
      .duration(HOVER_DURATION)
      .style('opacity', 1)
  }

  /**
   * Hide tooltip with animation
   */
  const hideTooltip = (tooltip: d3.Selection<HTMLDivElement, unknown, HTMLElement, any>) => {
    tooltip.transition().duration(HOVER_DURATION).style('opacity', 0)
  }

  /**
   * Create animated donut chart
   */
  const createDonutChart = (
    container: HTMLElement,
    data: DonutChartData[],
    dimensions: ChartDimensions,
    options: {
      innerRadius?: number
      outerRadius?: number
      startAngle?: number
      endAngle?: number
      showLabels?: boolean
      showTooltips?: boolean
    } = {},
  ) => {
    const {
      innerRadius = Math.min(dimensions.width, dimensions.height) * 0.3,
      outerRadius = Math.min(dimensions.width, dimensions.height) * 0.4,
      startAngle = 0,
      endAngle = 2 * Math.PI,
      showLabels = false,
      showTooltips = true,
    } = options

    const svg = createSvg(container, dimensions)
    const tooltip = showTooltips ? createTooltip() : null

    const g = svg
      .append('g')
      .attr('transform', `translate(${dimensions.width / 2}, ${dimensions.height / 2})`)

    const pie = d3
      .pie<DonutChartData>()
      .value((d) => d.value)
      .startAngle(startAngle)
      .endAngle(endAngle)
      .sort(null)

    const arc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius)

    const arcs = g.selectAll('.arc').data(pie(data)).enter().append('g').attr('class', 'arc')

    // Add paths with animation
    arcs
      .append('path')
      .attr('d', arc)
      .attr('fill', (d) => d.data.color)
      .style('opacity', 0)
      .transition()
      .duration(ANIMATION_DURATION)
      .style('opacity', 1)
      .attrTween('d', function (d) {
        const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d)
        return function (t) {
          return arc(interpolate(t))!
        }
      })

    // Add hover effects
    if (showTooltips && tooltip) {
      arcs
        .selectAll('path')
        .on('mouseover', function (event, d) {
          d3.select(this)
            .transition()
            .duration(HOVER_DURATION)
            .style('opacity', 0.8)
            .attr('transform', 'scale(1.05)')

          const total = data.reduce((sum, item) => sum + item.value, 0)
          const percentage = total > 0 ? Math.round((d.data.value / total) * 100) : 0
          showTooltip(tooltip, `${d.data.label}: ${d.data.value} (${percentage}%)`, event)
        })
        .on('mouseout', function () {
          d3.select(this)
            .transition()
            .duration(HOVER_DURATION)
            .style('opacity', 1)
            .attr('transform', 'scale(1)')

          hideTooltip(tooltip)
        })
    }

    return { svg, g, arcs }
  }

  /**
   * Create animated bar chart
   */
  const createBarChart = (
    container: HTMLElement,
    data: BarChartData[],
    dimensions: ChartDimensions,
    options: {
      color?: string
      showTooltips?: boolean
      yAxisLabel?: string
      xAxisLabel?: string
      maxValue?: number
    } = {},
  ) => {
    const {
      color = '#26A69A',
      showTooltips = true,
      yAxisLabel = '',
      xAxisLabel = '',
      maxValue,
    } = options

    const svg = createSvg(container, dimensions)
    const tooltip = showTooltips ? createTooltip() : null

    const innerWidth = dimensions.width - dimensions.margin.left - dimensions.margin.right
    const innerHeight = dimensions.height - dimensions.margin.top - dimensions.margin.bottom

    const g = svg
      .append('g')
      .attr('transform', `translate(${dimensions.margin.left}, ${dimensions.margin.top})`)

    // Scales
    const xScale = d3
      .scaleBand()
      .domain(data.map((d) => d.label))
      .range([0, innerWidth])
      .padding(0.1)

    const yScale = d3
      .scaleLinear()
      .domain([0, maxValue || d3.max(data, (d) => d.value) || 0])
      .range([innerHeight, 0])

    // Axes
    const xAxis = d3.axisBottom(xScale)
    const yAxis = d3.axisLeft(yScale)

    // Add X axis
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0, ${innerHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('font-size', '12px')

    // Add Y axis
    g.append('g').attr('class', 'y-axis').call(yAxis).selectAll('text').style('font-size', '12px')

    // Add axis labels
    if (xAxisLabel) {
      g.append('text')
        .attr('class', 'x-axis-label')
        .attr('text-anchor', 'middle')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + dimensions.margin.bottom - 5)
        .style('font-size', '14px')
        .text(xAxisLabel)
    }

    if (yAxisLabel) {
      g.append('text')
        .attr('class', 'y-axis-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -dimensions.margin.left + 15)
        .style('font-size', '14px')
        .text(yAxisLabel)
    }

    // Add bars with animation
    const bars = g
      .selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d) => xScale(d.label)!)
      .attr('width', xScale.bandwidth())
      .attr('y', innerHeight)
      .attr('height', 0)
      .attr('fill', color)
      .attr('rx', 4)
      .style('opacity', 0)

    // Animate bars
    bars
      .transition()
      .duration(ANIMATION_DURATION)
      .delay((d, i) => i * 100)
      .attr('y', (d) => yScale(d.value))
      .attr('height', (d) => innerHeight - yScale(d.value))
      .style('opacity', 1)

    // Add hover effects
    if (showTooltips && tooltip) {
      bars
        .on('mouseover', function (event, d) {
          d3.select(this)
            .transition()
            .duration(HOVER_DURATION)
            .style('opacity', 0.8)
            .attr('transform', 'scale(1.02)')

          showTooltip(tooltip, `${d.label}: ${d.value}`, event)
        })
        .on('mouseout', function () {
          d3.select(this)
            .transition()
            .duration(HOVER_DURATION)
            .style('opacity', 1)
            .attr('transform', 'scale(1)')

          hideTooltip(tooltip)
        })
    }

    return { svg, g, bars, xScale, yScale }
  }

  /**
   * Update chart data with smooth transitions
   */
  const updateDonutChart = (
    arcs: d3.Selection<SVGGElement, d3.PieArcDatum<DonutChartData>, SVGGElement, unknown>,
    newData: DonutChartData[],
    arc: d3.Arc<any, d3.PieArcDatum<DonutChartData>>,
  ) => {
    const pie = d3
      .pie<DonutChartData>()
      .value((d) => d.value)
      .sort(null)

    arcs
      .data(pie(newData))
      .select('path')
      .transition()
      .duration(ANIMATION_DURATION)
      .attrTween('d', function (d) {
        const interpolate = d3.interpolate(this._current || { startAngle: 0, endAngle: 0 }, d)
        this._current = interpolate(0)
        return function (t) {
          return arc(interpolate(t))!
        }
      })
  }

  /**
   * Setup responsive behavior
   */
  const makeResponsive = (container: HTMLElement, updateCallback: () => void) => {
    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
    }

    resizeObserver.value = new ResizeObserver(() => {
      updateCallback()
    })

    resizeObserver.value.observe(container)
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
    }
    d3.select('body').select('.d3-tooltip').remove()
  }

  return {
    createSvg,
    createTooltip,
    showTooltip,
    hideTooltip,
    createDonutChart,
    createBarChart,
    updateDonutChart,
    makeResponsive,
    cleanup,
    resizeObserver,
  }
}
