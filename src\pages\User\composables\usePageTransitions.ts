import { ref, nextTick } from 'vue'

export interface TransitionOptions {
  duration?: number
  easing?: string
  delay?: number
}

/**
 * Composable for page transition animations
 */
export function usePageTransitions() {
  const isTransitioning = ref(false)

  /**
   * Fade in animation for page elements
   */
  const fadeIn = (
    element: HTMLElement,
    options: TransitionOptions = {}
  ): Promise<void> => {
    const { duration = 500, easing = 'ease-out', delay = 0 } = options

    return new Promise((resolve) => {
      element.style.opacity = '0'
      element.style.transform = 'translateY(20px)'
      element.style.transition = `opacity ${duration}ms ${easing} ${delay}ms, transform ${duration}ms ${easing} ${delay}ms`

      nextTick(() => {
        element.style.opacity = '1'
        element.style.transform = 'translateY(0)'

        setTimeout(() => {
          resolve()
        }, duration + delay)
      })
    })
  }

  /**
   * Fade out animation for page elements
   */
  const fadeOut = (
    element: HTMLElement,
    options: TransitionOptions = {}
  ): Promise<void> => {
    const { duration = 300, easing = 'ease-in', delay = 0 } = options

    return new Promise((resolve) => {
      element.style.transition = `opacity ${duration}ms ${easing} ${delay}ms, transform ${duration}ms ${easing} ${delay}ms`
      element.style.opacity = '0'
      element.style.transform = 'translateY(-20px)'

      setTimeout(() => {
        resolve()
      }, duration + delay)
    })
  }

  /**
   * Slide in from left animation
   */
  const slideInLeft = (
    element: HTMLElement,
    options: TransitionOptions = {}
  ): Promise<void> => {
    const { duration = 600, easing = 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', delay = 0 } = options

    return new Promise((resolve) => {
      element.style.opacity = '0'
      element.style.transform = 'translateX(-100px)'
      element.style.transition = `opacity ${duration}ms ${easing} ${delay}ms, transform ${duration}ms ${easing} ${delay}ms`

      nextTick(() => {
        element.style.opacity = '1'
        element.style.transform = 'translateX(0)'

        setTimeout(() => {
          resolve()
        }, duration + delay)
      })
    })
  }

  /**
   * Slide in from right animation
   */
  const slideInRight = (
    element: HTMLElement,
    options: TransitionOptions = {}
  ): Promise<void> => {
    const { duration = 600, easing = 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', delay = 0 } = options

    return new Promise((resolve) => {
      element.style.opacity = '0'
      element.style.transform = 'translateX(100px)'
      element.style.transition = `opacity ${duration}ms ${easing} ${delay}ms, transform ${duration}ms ${easing} ${delay}ms`

      nextTick(() => {
        element.style.opacity = '1'
        element.style.transform = 'translateX(0)'

        setTimeout(() => {
          resolve()
        }, duration + delay)
      })
    })
  }

  /**
   * Scale in animation
   */
  const scaleIn = (
    element: HTMLElement,
    options: TransitionOptions = {}
  ): Promise<void> => {
    const { duration = 400, easing = 'cubic-bezier(0.34, 1.56, 0.64, 1)', delay = 0 } = options

    return new Promise((resolve) => {
      element.style.opacity = '0'
      element.style.transform = 'scale(0.8)'
      element.style.transition = `opacity ${duration}ms ${easing} ${delay}ms, transform ${duration}ms ${easing} ${delay}ms`

      nextTick(() => {
        element.style.opacity = '1'
        element.style.transform = 'scale(1)'

        setTimeout(() => {
          resolve()
        }, duration + delay)
      })
    })
  }

  /**
   * Stagger animation for multiple elements
   */
  const staggerIn = (
    elements: HTMLElement[],
    animationType: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'scaleIn' = 'fadeIn',
    staggerDelay: number = 100,
    options: TransitionOptions = {}
  ): Promise<void[]> => {
    const animations = {
      fadeIn,
      slideInLeft,
      slideInRight,
      scaleIn
    }

    const animationFunction = animations[animationType]

    return Promise.all(
      elements.map((element, index) =>
        animationFunction(element, {
          ...options,
          delay: (options.delay || 0) + index * staggerDelay
        })
      )
    )
  }

  /**
   * Chart container animation
   */
  const animateChartContainer = (
    container: HTMLElement,
    options: TransitionOptions = {}
  ): Promise<void> => {
    const { duration = 800, delay = 200 } = options

    return new Promise((resolve) => {
      container.style.opacity = '0'
      container.style.transform = 'translateY(30px) scale(0.95)'
      container.style.transition = `opacity ${duration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94) ${delay}ms, transform ${duration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94) ${delay}ms`

      nextTick(() => {
        container.style.opacity = '1'
        container.style.transform = 'translateY(0) scale(1)'

        setTimeout(() => {
          resolve()
        }, duration + delay)
      })
    })
  }

  /**
   * Page transition orchestrator
   */
  const transitionPage = async (
    enterElements: HTMLElement[],
    exitElements: HTMLElement[] = [],
    options: {
      enterAnimation?: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'scaleIn'
      exitAnimation?: 'fadeOut'
      staggerDelay?: number
      enterDelay?: number
      exitDelay?: number
    } = {}
  ) => {
    const {
      enterAnimation = 'fadeIn',
      exitAnimation = 'fadeOut',
      staggerDelay = 100,
      enterDelay = 0,
      exitDelay = 0
    } = options

    isTransitioning.value = true

    try {
      // Exit animations
      if (exitElements.length > 0) {
        await Promise.all(
          exitElements.map((element, index) =>
            fadeOut(element, { delay: exitDelay + index * 50 })
          )
        )
      }

      // Enter animations
      await staggerIn(enterElements, enterAnimation, staggerDelay, { delay: enterDelay })
    } finally {
      isTransitioning.value = false
    }
  }

  return {
    isTransitioning,
    fadeIn,
    fadeOut,
    slideInLeft,
    slideInRight,
    scaleIn,
    staggerIn,
    animateChartContainer,
    transitionPage
  }
}
