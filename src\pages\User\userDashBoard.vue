<template>
  <UserNavigation />
  <div class="q-pa-md">
    <div class="container q-pa-md">
      <!-- Main Layout: 4:6 Column Ratio -->
      <div class="row q-col-gutter-md justify-between">
        <!-- Left Column (4 parts) -->
        <div class="col-12 col-lg-4 left-side">
          <!-- Summary Cards -->
          <div class="row q-col-gutter-sm q-mb-sm">
            <!-- Total Employees Card -->
            <div class="col-12 col-md-6">
              <q-card flat class="summary-card">
                <q-card-section class="text-center flex flex-center column" style="height: 100%">
                  <div class="row items-center justify-center q-mb-sm">
                    <q-icon name="badge" size="md" class="text-primary q-mr-sm" />
                    <span class="text-subtitle2">จำนวนพนักงาน</span>
                  </div>
                  <div v-if="loading.workStats" class="q-my-md">
                    <q-skeleton type="text" width="60px" height="48px" class="text-h3" />
                  </div>
                  <div v-else class="text-h3 text-weight-bold">{{ workStats.totalEmployees }}</div>
                  <div class="text-caption text-grey-6">คน</div>
                </q-card-section>
              </q-card>
            </div>

            <!-- Average Work Hours Card -->
            <div class="col-12 col-md-6">
              <q-card flat class="summary-card">
                <q-card-section class="text-center flex flex-center column" style="height: 100%">
                  <div class="row items-center justify-center q-mb-sm">
                    <q-icon name="schedule" size="md" class="text-orange q-mr-sm" />
                    <span class="text-subtitle2">ชั่วโมงทำงานเฉลี่ย</span>
                  </div>
                  <div v-if="loading.workStats" class="q-my-md">
                    <q-skeleton type="text" width="60px" height="48px" class="text-h3" />
                  </div>
                  <div v-else class="text-h3 text-weight-bold">
                    {{ workStats.averageHours }}
                  </div>
                  <div class="text-caption text-grey-6">ชั่วโมง/คน</div>
                </q-card-section>
              </q-card>
            </div>
          </div>

          <!-- Number of Employees Graph (Donut Chart) -->
          <div class="col-12">
            <q-card flat class="chart-card">
              <q-card-section>
                <div class="text-subtitle1 text-weight-medium q-mb-md text-center">
                  กราฟแสดงจำนวนพนักงาน
                </div>
                <div class="chart-container-gauge">
                  <div v-if="loading.workStats" class="flex flex-center" style="height: 180px">
                    <q-spinner-dots color="primary" size="40px" />
                  </div>
                  <canvas v-else ref="gaugeChart" width="300" height="180"></canvas>
                </div>
                <div v-if="!loading.workStats" class="gauge-info q-mt-md">
                  <div class="row justify-center q-col-gutter-sm">
                    <div class="col-auto">
                      <div class="gauge-stat">
                        <div class="gauge-stat-value text-h6 text-weight-bold text-teal">
                          {{ employeeData.fullTime }}
                        </div>
                        <div class="gauge-stat-label text-caption">พนักงานประจำ</div>
                      </div>
                    </div>
                    <div class="col-auto">
                      <div class="gauge-stat">
                        <div class="gauge-stat-value text-h6 text-weight-bold text-purple">
                          {{ employeeData.partTime }}
                        </div>
                        <div class="gauge-stat-label text-caption">พนักงานพาร์ทไทม์</div>
                      </div>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Right Column (6 parts) -->
        <div class="col-12 col-lg-6 right-side">
          <!-- Month Selector at Top -->
          <div class="q-mb-sm">
            <q-card flat class="date-navigation-card">
              <q-card-section class="text-center">
                <div class="date-navigation">
                  <q-btn flat round icon="chevron_left" @click="previousMonth" />
                  <span class="text-subtitle1 q-mx-md"
                    >{{ formattedStartDate }} ถึง {{ formattedEndDate }}</span
                  >
                  <q-btn flat round icon="chevron_right" @click="nextMonth" />
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Additional Graphs in Right Column -->
          <div class="row q-col-gutter-sm">
            <!-- Working Hours by Employee Chart -->
            <div class="col-12 q-mb-sm">
              <q-card flat class="chart-card">
                <q-card-section>
                  <div class="text-subtitle1 text-weight-medium q-mb-md text-center">
                    กราฟแสดงจำนวนชั่วโมงทำงาน
                  </div>
                  <div class="chart-container-bar">
                    <div v-if="loading.workHours" class="flex flex-center" style="height: 250px">
                      <q-spinner-dots color="primary" size="40px" />
                    </div>
                    <canvas v-else ref="workHoursChart" width="600" height="250"></canvas>
                  </div>
                </q-card-section>
              </q-card>
            </div>

            <!-- Late Check-ins by Employee Chart -->
            <div class="col-12">
              <q-card flat class="chart-card">
                <q-card-section>
                  <div class="text-subtitle1 text-weight-medium q-mb-md text-center">
                    กราฟแสดงจำนวนวัน ที่ Checkin เกินเวลา
                  </div>
                  <div class="chart-container-bar">
                    <div v-if="loading.lateCheckIn" class="flex flex-center" style="height: 250px">
                      <q-spinner-dots color="primary" size="40px" />
                    </div>
                    <canvas v-else ref="lateCheckInChart" width="600" height="250"></canvas>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row justify-center q-mt-lg">
      <q-btn
        to="/user/summary"
        unelevated
        icon="arrow_back"
        label="ย้อนกลับ"
        class="dashboard-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import UserNavigation from 'src/components/userNavigation.vue'
import { AttendanceService } from 'src/services/attendanceService'
import { useQuasar } from 'quasar'
import {
  Chart,
  ArcElement,
  Tooltip,
  Legend,
  DoughnutController,
  BarElement,
  CategoryScale,
  LinearScale,
  BarController,
} from 'chart.js'

// Register Chart.js components
Chart.register(
  ArcElement,
  Tooltip,
  Legend,
  DoughnutController,
  BarElement,
  CategoryScale,
  LinearScale,
  BarController,
)

// Quasar instance for notifications
const $q = useQuasar()

// Chart references
const gaugeChart = ref<HTMLCanvasElement>()
const workHoursChart = ref<HTMLCanvasElement>()
const lateCheckInChart = ref<HTMLCanvasElement>()

// Chart instances
let gaugeChartInstance: Chart | null = null
let workHoursChartInstance: Chart | null = null
let lateCheckInChartInstance: Chart | null = null

// Date navigation
const currentDate = ref(new Date())
const startDate = ref(new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1))
const endDate = ref(new Date(currentDate.value))

// Format dates for API calls
const formattedStartDate = computed(() => {
  const day = startDate.value.getDate()
  const month = thaiMonths[startDate.value.getMonth()]
  const year = startDate.value.getFullYear() + 543 // Convert to Buddhist Era
  return `${day} ${month} ${year}`
})

const formattedEndDate = computed(() => {
  const day = endDate.value.getDate()
  const month = thaiMonths[endDate.value.getMonth()]
  const year = endDate.value.getFullYear() + 543 // Convert to Buddhist Era
  return `${day} ${month} ${year}`
})

// Thai month names
const thaiMonths = [
  'มกราคม',
  'กุมภาพันธ์',
  'มีนาคม',
  'เมษายน',
  'พฤษภาคม',
  'มิถุนายน',
  'กรกฎาคม',
  'สิงหาคม',
  'กันยายน',
  'ตุลาคม',
  'พฤศจิกายน',
  'ธันวาคม',
]

// Format dates for API calls
const apiStartDate = computed(() => {
  return startDate.value.toISOString().split('T')[0]
})

const apiEndDate = computed(() => {
  return endDate.value.toISOString().split('T')[0]
})

// Loading states
const loading = ref({
  workStats: false,
  employeeData: false,
  workHours: false,
  lateCheckIn: false,
})

// Define interface for the expected API response
interface MonthlyHoursResponse {
  summary: {
    totalEmployees: number
    averageWorkHours: number
    employeeTypes: {
      fullTime: number
      partTime: number
    }
    totalLateDays: number
  }
  employees: Array<{
    userId: number
    name: string
    totalWorkHours: number
    lateDays: number
  }>
}

// Work statistics (dynamic data from API)
const workStats = ref({
  totalEmployees: 0,
  averageHours: 0,
  totalLateDays: 0,
})

// Chart data (dynamic data from API)
const employeeData = ref({
  fullTime: 0,
  partTime: 0,
})

// Chart data for employee-specific metrics
const workHoursData = ref<Array<{ employeeId: number; hours: number; employeeName: string }>>([])
const lateCheckInData = ref<Array<{ employeeId: number; lateDays: number; employeeName: string }>>(
  [],
)
const employeesData = ref<
  Array<{ userId: number; name: string; totalWorkHours: number; lateDays: number }>
>([])

// Main API data fetching function using fetchMonthlyHoursByDateRange
const fetchMonthlyHoursData = async () => {
  loading.value.workStats = true
  loading.value.workHours = true
  loading.value.lateCheckIn = true

  try {
    // Use the fetchMonthlyHoursByDateRange API with current date range
    const startDateStr = apiStartDate.value
    const endDateStr = apiEndDate.value

    // Ensure dates are valid before making API call
    if (!startDateStr || !endDateStr) {
      console.error('Invalid date range:', { startDateStr, endDateStr })
      resetToEmptyState()
      return
    }

    console.log('Fetching monthly hours data for:', { startDateStr, endDateStr })

    const monthlyHoursResponse: MonthlyHoursResponse =
      await AttendanceService.getMonthlyHoursByDateRange(startDateStr, endDateStr)

    console.log('Monthly hours response:', monthlyHoursResponse)

    if (monthlyHoursResponse && monthlyHoursResponse.summary) {
      // Update work statistics from API response
      workStats.value.totalEmployees = monthlyHoursResponse.summary.totalEmployees || 0
      workStats.value.averageHours = monthlyHoursResponse.summary.averageWorkHours || 0
      workStats.value.totalLateDays = monthlyHoursResponse.summary.totalLateDays || 0

      // Update employee type distribution
      employeeData.value.fullTime = monthlyHoursResponse.summary.employeeTypes?.fullTime || 0
      employeeData.value.partTime = monthlyHoursResponse.summary.employeeTypes?.partTime || 0

      // Store employees data for chart processing
      employeesData.value = monthlyHoursResponse.employees || []

      // Process data for charts
      processChartData(monthlyHoursResponse)
    } else {
      // Fallback: if the API doesn't return the expected structure, show empty state
      console.warn('API response does not match expected structure, showing empty state')
      resetToEmptyState()
    }
  } catch (error) {
    console.error('Error fetching monthly hours data:', error)
    $q.notify({
      type: 'negative',
      message: 'ไม่สามารถโหลดข้อมูลสถิติการทำงานได้',
      position: 'top',
    })
    // Show empty state on error
    resetToEmptyState()
  } finally {
    loading.value.workStats = false
    loading.value.workHours = false
    loading.value.lateCheckIn = false
  }
}

// Reset to empty state helper function
const resetToEmptyState = () => {
  workStats.value = { totalEmployees: 0, averageHours: 0, totalLateDays: 0 }
  employeeData.value = { fullTime: 0, partTime: 0 }
  workHoursData.value = []
  lateCheckInData.value = []
  employeesData.value = []
}

// Process chart data from the API response for employee-specific metrics
const processChartData = (monthlyHoursResponse: MonthlyHoursResponse) => {
  try {
    if (monthlyHoursResponse.employees && Array.isArray(monthlyHoursResponse.employees)) {
      // Process data for Chart 1: Working Hours by Employee
      workHoursData.value = monthlyHoursResponse.employees
        .map((employee) => ({
          employeeId: employee.userId,
          hours: employee.totalWorkHours,
          employeeName: employee.name || `Employee ${employee.userId}`, // Use actual name from API
        }))
        .sort((a, b) => a.employeeId - b.employeeId)

      // Process data for Chart 2: Late Check-ins by Employee
      lateCheckInData.value = monthlyHoursResponse.employees
        .map((employee) => ({
          employeeId: employee.userId,
          lateDays: employee.lateDays,
          employeeName: employee.name || `Employee ${employee.userId}`, // Use actual name from API
        }))
        .sort((a, b) => a.employeeId - b.employeeId)

      console.log('Processed chart data:', {
        workHours: workHoursData.value,
        lateCheckIns: lateCheckInData.value,
      })
    } else {
      // Reset to empty arrays if no employee data
      workHoursData.value = []
      lateCheckInData.value = []
    }
  } catch (error) {
    console.error('Error processing chart data:', error)
    // Reset to empty arrays on error
    workHoursData.value = []
    lateCheckInData.value = []
  }
}

// Date navigation functions
const previousMonth = async () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate

  // Update start and end dates
  startDate.value = new Date(newDate.getFullYear(), newDate.getMonth(), 1)
  endDate.value = new Date(newDate.getFullYear(), newDate.getMonth() + 1, 0) // Last day of month

  // Refresh data and charts with new date range
  await fetchMonthlyHoursData()
  await refreshCharts()
}

const nextMonth = async () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate

  // Update start and end dates
  startDate.value = new Date(newDate.getFullYear(), newDate.getMonth(), 1)
  endDate.value = new Date(newDate.getFullYear(), newDate.getMonth() + 1, 0) // Last day of month

  // Refresh data and charts with new date range
  await fetchMonthlyHoursData()
  await refreshCharts()
}

// Chart creation functions
const createGaugeChart = async () => {
  await nextTick()

  if (gaugeChartInstance) {
    gaugeChartInstance.destroy()
  }

  if (gaugeChart.value) {
    const ctx = gaugeChart.value.getContext('2d')
    if (ctx) {
      const total = employeeData.value.fullTime + employeeData.value.partTime

      gaugeChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['พนักงานประจำ', 'พนักงานพาร์ทไทม์', 'Empty'],
          datasets: [
            {
              data: [
                employeeData.value.fullTime,
                employeeData.value.partTime,
                Math.max(0, total - employeeData.value.fullTime - employeeData.value.partTime),
              ],
              backgroundColor: ['#26A69A', '#AB47BC', 'transparent'],
              borderWidth: 0,
              circumference: 180,
              rotation: 270,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: '75%',
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              filter: function (tooltipItem) {
                return tooltipItem.dataIndex !== 2 // Hide tooltip for empty segment
              },
              callbacks: {
                label: function (context: { label?: string; parsed: number }) {
                  const label = context.label || ''
                  const value = context.parsed
                  const total = employeeData.value.fullTime + employeeData.value.partTime
                  const percentage = total > 0 ? Math.round((value / total) * 100) : 0
                  return `${label}: ${value} คน (${percentage}%)`
                },
              },
            },
          },
        },
      })
    }
  }
}

const createWorkHoursChart = async () => {
  await nextTick()

  if (workHoursChartInstance) {
    workHoursChartInstance.destroy()
  }

  if (workHoursChart.value) {
    const ctx = workHoursChart.value.getContext('2d')
    if (ctx) {
      // Calculate dynamic max value based on data
      const maxHours =
        workHoursData.value.length > 0
          ? Math.max(...workHoursData.value.map((item) => item.hours))
          : 50
      const chartMax = Math.ceil(maxHours * 1.2) // Add 20% padding

      workHoursChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: workHoursData.value.map((item) => `ID: ${item.employeeId}`),
          datasets: [
            {
              label: 'Working Hours',
              data: workHoursData.value.map((item) => item.hours),
              backgroundColor: '#26A69A',
              borderColor: '#1E8E7E',
              borderWidth: 1,
              borderRadius: 4,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#ffffff',
              bodyColor: '#ffffff',
              borderColor: '#26A69A',
              borderWidth: 1,
              cornerRadius: 6,
              displayColors: false,
              callbacks: {
                title: function (context) {
                  const index = context[0]?.dataIndex
                  if (index !== undefined && workHoursData.value[index]) {
                    const employee = workHoursData.value[index]
                    return employee.employeeName
                  }
                  return 'Employee'
                },
                label: function (context) {
                  const index = context.dataIndex
                  if (index !== undefined && workHoursData.value[index]) {
                    const employee = workHoursData.value[index]
                    return [
                      `Employee ID: ${employee.employeeId}`,
                      `Working Hours: ${context.parsed.y} hours`,
                    ]
                  }
                  return `Working Hours: ${context.parsed.y} hours`
                },
              },
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              max: chartMax,
              title: {
                display: true,
                text: 'Total Working Hours',
              },
              ticks: {
                stepSize: Math.ceil(chartMax / 10),
              },
            },
            x: {
              title: {
                display: true,
                text: 'Employee ID',
              },
              grid: {
                display: false,
              },
            },
          },
        },
      })
    }
  }
}

const createLateCheckInChart = async () => {
  await nextTick()

  if (lateCheckInChartInstance) {
    lateCheckInChartInstance.destroy()
  }

  if (lateCheckInChart.value) {
    const ctx = lateCheckInChart.value.getContext('2d')
    if (ctx) {
      // Calculate dynamic max value based on data
      const maxLateDays =
        lateCheckInData.value.length > 0
          ? Math.max(...lateCheckInData.value.map((item) => item.lateDays))
          : 8
      const chartMax = Math.ceil(maxLateDays * 1.2) // Add 20% padding

      lateCheckInChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: lateCheckInData.value.map((item) => `ID: ${item.employeeId}`),
          datasets: [
            {
              label: 'Late Days',
              data: lateCheckInData.value.map((item) => item.lateDays),
              backgroundColor: '#AB47BC',
              borderColor: '#9C27B0',
              borderWidth: 1,
              borderRadius: 4,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#ffffff',
              bodyColor: '#ffffff',
              borderColor: '#AB47BC',
              borderWidth: 1,
              cornerRadius: 6,
              displayColors: false,
              callbacks: {
                title: function (context) {
                  const index = context[0]?.dataIndex
                  if (index !== undefined && lateCheckInData.value[index]) {
                    const employee = lateCheckInData.value[index]
                    return employee.employeeName
                  }
                  return 'Employee'
                },
                label: function (context) {
                  const index = context.dataIndex
                  if (index !== undefined && lateCheckInData.value[index]) {
                    const employee = lateCheckInData.value[index]
                    return [
                      `Employee ID: ${employee.employeeId}`,
                      `Late Days: ${context.parsed.y} days`,
                    ]
                  }
                  return `Late Days: ${context.parsed.y} days`
                },
              },
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              max: chartMax,
              title: {
                display: true,
                text: 'Number of Late Days',
              },
              ticks: {
                stepSize: Math.max(1, Math.ceil(chartMax / 10)),
              },
            },
            x: {
              title: {
                display: true,
                text: 'Employee ID',
              },
              grid: {
                display: false,
              },
            },
          },
        },
      })
    }
  }
}

// Refresh all charts
const refreshCharts = async () => {
  await createGaugeChart()
  await createWorkHoursChart()
  await createLateCheckInChart()
}

// Initialize all data and charts
const initializeDashboard = async () => {
  await fetchMonthlyHoursData()
  await refreshCharts()
}

// Initialize charts on mount
onMounted(async () => {
  await initializeDashboard()
})
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
  max-width: 100%;
  overflow-x: hidden;
}

.date-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-navigation-card {
  background-color: #e1edea;
  border-radius: 10px;
  min-height: 60px;
}

.summary-card {
  background-color: #e1edea;
  border-radius: 10px;
  height: 100%;
  min-height: 200px;
}

.chart-card {
  background-color: #e1edea;
  border-radius: 10px;
  height: 100%;
}

.chart-container-gauge {
  position: relative;
  height: 290px;
  width: 300px;
  margin: 0 auto;
}

.chart-container-bar {
  position: relative;
  height: 200px;
  width: 100%;
}

.gauge-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gauge-stat {
  text-align: center;
  padding: 8px;
}

.gauge-stat-value {
  font-size: 1.2rem;
  line-height: 1.2;
}

.gauge-stat-label {
  margin-top: 4px;
  opacity: 0.8;
}

.bg-teal {
  background-color: #26a69a;
}

.bg-purple {
  background-color: #ab47bc;
}

.left-side {
  width: 100%;
  max-width: 650px;
}

.right-side {
  width: 100%;
  max-width: 880px;
}

@media (max-width: 1500px) {
  .left-side {
    max-width: 490px;
  }

  .right-side {
    max-width: 600px;
  }
}

@media (max-width: 1200px) {
  .left-side {
    max-width: 400px;
  }
  .right-side {
    max-width: 500px;
  }
}

/* Responsive adjustments */
@media (max-width: 1199px) {
  .chart-container-gauge {
    height: 160px;
    width: 280px;
  }

  .chart-container-bar {
    height: 180px;
  }
}

@media (max-width: 1023px) {
  /* Stack columns vertically on smaller screens */
  .col-lg-4,
  .col-lg-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .chart-container-gauge {
    height: 150px;
    width: 260px;
  }

  .chart-container-bar {
    height: 170px;
  }
}

@media (max-width: 599px) {
  .date-navigation {
    flex-direction: column;
    gap: 8px;
  }

  .chart-container-gauge {
    height: 120px;
    width: 220px;
  }

  .chart-container-bar {
    height: 150px;
  }

  .date-navigation-card {
    min-height: 100px;
  }

  .summary-card {
    min-height: 80px;
  }

  .gauge-stat-value {
    font-size: 1rem;
  }
}
/* ...existing code... */
.dashboard-btn {
  background-color: #609fa3;
  color: white;
  border-radius: 10px;
  padding: 10px 20px;
}
</style>
